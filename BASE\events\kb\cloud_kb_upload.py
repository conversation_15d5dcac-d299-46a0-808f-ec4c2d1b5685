def upload_to_cloud(
        self,
        session: str,
        processed_data: list[QdrantKnowledgeBaseChunk],
        progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
        retries: int = 10,
    ):
        """Sync the knowledge base to cloud and return cloud ID."""

        upload_bin = {
            "name": self.name,
            "type": self.type,
            "sync": self.syncConfig.enabled,
            "shared": self.scope == QdrantKnowledgebaseScope.Organization,
            "processed_chunk": [
                {
                    "vectors": chunk.embeddings,
                    "metadata": {
                        "id": chunk.metadata.id,
                        "content": chunk.metadata.content,
                        "file": chunk.metadata.file,
                        "name": chunk.metadata.name,
                        "additional_metadata": chunk.metadata.additional_metadata,
                    },
                }
                for chunk in processed_data
            ],
        }

        LOGGER.info(f"Uploading to cloud: {upload_bin}")

        error = None
        while retries > 0:
            try:
                # Initialize progress
                await progress_fn(0)

                LOGGER.info("Sending to cloud")

                await asyncio.sleep(1)

                base_url = G_BASE_URL.get().codebase

                async with httpx.AsyncClient(
                    verify=SSL_CERT_FILE,
                    timeout=300000.0,
                ) as client:
                    req = await client.post(
                        f"{base_url}/upload/kb",
                        json=upload_bin,
                        headers={
                            "x-session": session,
                            "Content-Type": "application/json",
                        },
                    )
                await asyncio.sleep(1)

                LOGGER.info(f"Response: {req.status_code}")
                LOGGER.info(f"Response: {req.json()}")

                res = req.json()
                LOGGER.info(f"Status Code: {req.status_code}")
                if req.status_code != 200:
                    raise RuntimeError(res["error"])

                LOGGER.info(f"RES: {res}")

                # Update progress to complete
                await asyncio.sleep(1)
                self.progress = QdrantProgressInfo(
                    message="Upload complete", status="complete", progress=1.0
                )
                await progress_fn(95.0)

                LOGGER.info("Upload complete")

                # Update the local knowledge base ID with cloud ID
                new_id = res["cloud_index"]
                self.update_cloud_id(new_id)

                return
            except (httpx.ConnectError, httpx.ReadError, ConnectionResetError) as e:
                print(f"Connection error: {e}")
                LOGGER.error(f"Connection error: {e}")
                error = e
                retries -= 1
                if retries > 0:
                    wait_time = 2 ** (10 - retries)  # Exponential backoff
                    print(
                        f"Retrying in {wait_time} seconds... ({retries} retries left)"
                    )
                    time.sleep(wait_time)

                # Update progress to error state
                self.progress = QdrantProgressInfo(
                    message=f"Connection error: {str(e)}", status="error", progress=0.0
                )
                await progress_fn(0.0)
            except Exception as e:
                print(e)
                LOGGER.error(e)
                error = e
                retries -= 1
                time.sleep(1)

                # Update progress to error state
                self.progress = QdrantProgressInfo(
                    message=f"Upload failed: {str(e)}", status="error", progress=0.0
                )
                await progress_fn(0.0)

            await asyncio.sleep(1)
        if error:
            raise error

    LOGGER.info("Sync to cloud complete (Exit)")


def _validate_kb_for_cloud_sync(kb_id: str) -> tuple[bool, str, Optional[QdrantKnowledgeBase]]:
    """
    Validate that a knowledge base can be synced to cloud.

    Returns:
        tuple: (is_valid, error_message, kb_object)
    """
    try:
        # Check if KB exists
        if not QdrantKnowledgeBase.exists_id(kb_id):
            return False, f"Knowledge base with ID {kb_id} not found", None

        # Get the KB object
        kb = QdrantKnowledgeBase.get(kb_id)

        # Check if KB is local (not remote)
        if kb.source.value.upper() == "REMOTE":
            return False, "Cannot sync remote knowledge bases - they are already in the cloud", None

        # Check if KB has a cloud_id (required for sync)
        if not kb.cloud_id:
            return False, f"Knowledge base does not have a cloud ID. Upload to cloud first before syncing.", None

        # All validations passed
        return True, "", kb

    except Exception as e:
        LOGGER.error(f"Error validating KB for cloud sync: {e}")
        return False, f"Error validating knowledge base: {str(e)}", None



@logger.catch()
async def handle_upload_to_cloud_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):
    """Handle uploading an existing local knowledge base to the cloud."""

    # -----------------------------------------------------------------------------------------
    # 1. Validate Knowledge Base for Cloud Upload --------------------------------------------
    # -----------------------------------------------------------------------------------------

    is_valid, error_message, kb = _validate_kb_for_cloud_upload(cloud_upload_data.kb_id)
    if not is_valid:
        LOGGER.error(f"KB validation failed: {error_message}")
        await sio.emit(
            "upload_to_cloud:error",
            data=CloudUploadResponseData(
                request_id=cloud_upload_data.request_id,
                status="error",
                message=error_message,
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 2. Retrieve Existing Chunks from Knowledge Base ---------------------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "upload_to_cloud:progress",
        data=CloudUploadProgressData(
            request_id=cloud_upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=10,
            message="Retrieving knowledge base chunks",
        ).model_dump(),
        to=sid,
    )

    try:
        chunks_retrieval_start = time.time()
        LOGGER.info(f"Retrieving chunks for KB: {kb.name}")
        chunks = kb.get_all_chunks()
        chunks_retrieval_time = time.time() - chunks_retrieval_start

        LOGGER.info(
            f"Retrieved {len(chunks)} chunks from KB {kb.name} in {chunks_retrieval_time:.2f}s"
        )

        if not chunks:
            raise ValueError("No chunks found in knowledge base")

    except Exception as e:
        LOGGER.error(f"Error retrieving chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "upload_to_cloud:error",
            data=CloudUploadResponseData(
                request_id=cloud_upload_data.request_id,
                status="error",
                message=f"Failed to retrieve knowledge base chunks: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 3. Update Sync Configuration if Provided ----------------------------------------------
    # -----------------------------------------------------------------------------------------

    if cloud_upload_data.sync_config:
        kb.syncConfig = cloud_upload_data.sync_config
        LOGGER.info(f"Updated sync config for KB: {kb.syncConfig}")

    # Enable cloud sync for the upload
    kb.syncConfig.enabled = True

    # -----------------------------------------------------------------------------------------
    # 4. Upload to Cloud ---------------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    cloud_sync_start = time.time()
    LOGGER.info("Starting upload to cloud")

    progress_fn = lambda progress: sio.emit(
        "upload_to_cloud:progress",
        data=CloudUploadProgressData(
            request_id=cloud_upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=progress,
            message="Uploading to cloud",
        ).model_dump(),
        to=sid,
    )

    try:
        LOGGER.debug(f"Syncing to cloud with session: {cloud_upload_data.session}")

        await kb.upload_to_cloud(cloud_upload_data.session, chunks, progress_fn=progress_fn)

        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.info(
            f"Cloud upload completed successfully in {cloud_sync_time:.2f} seconds"
        )
    except Exception as e:
        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.error(f"ERROR FROM sync_to_cloud after {cloud_sync_time:.2f}s: {e}")
        LOGGER.error(f"Cloud sync error traceback: {traceback.format_exc()}")
        await sio.emit(
            "upload_to_cloud:error",
            data=CloudUploadResponseData(
                request_id=cloud_upload_data.request_id,
                status="error",
                message=f"Failed to upload to cloud: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 5. Report Success to the Client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    LOGGER.info(
        f"Cloud upload process completed successfully for knowledge base: {kb.name}"
    )
    LOGGER.info(f"Uploaded {len(chunks)} chunks to cloud")

    # Report success to the client
    await sio.emit(
        "upload_to_cloud:success",
        data=CloudUploadResponseData(
            request_id=cloud_upload_data.request_id,
            status="success",
            message="Knowledge base uploaded to cloud successfully",
            data=kb.model_dump(),
        ).model_dump(),
        to=sid,
    )
    LOGGER.info(
        f"Success event emitted to client for request: {cloud_upload_data.request_id}"
    )