def _get_modified_chunks_for_sync(
    kb: QdrantKnowledgeBase,
    all_chunks: list[QdrantKnowledgeBaseChunk],
    buffer_time_ms: int = 5000  # 5 second buffer to avoid edge cases
) -> list[QdrantKnowledgeBaseChunk]:
    """
    Get chunks from files that have been modified since the last sync.

    Args:
        kb: The knowledge base object
        all_chunks: All chunks from the knowledge base
        buffer_time_ms: Buffer time in milliseconds to avoid edge cases

    Returns:
        List of chunks from modified files only
    """
    try:
        # Get last sync timestamp
        last_synced = kb.syncConfig.lastSynced
        if last_synced <= 0:
            # If never synced, return all chunks
            LOGGER.info("Knowledge base has never been synced, returning all chunks")
            return all_chunks

        # Get file timestamps from metadata
        if not kb.metadata or not hasattr(kb.metadata, 'file_timestamps'):
            LOGGER.warning("No file timestamps available, returning all chunks")
            return all_chunks

        file_timestamps = kb.metadata.file_timestamps
        if not file_timestamps:
            LOGGER.warning("File timestamps dictionary is empty, returning all chunks")
            return all_chunks

        # Find modified files (with buffer time)
        sync_threshold = last_synced - buffer_time_ms
        modified_files = set()

        for file_path, file_timestamp in file_timestamps.items():
            if file_timestamp > sync_threshold:
                modified_files.add(file_path)

        LOGGER.info(f"Found {len(modified_files)} modified files since last sync")
        LOGGER.debug(f"Modified files: {list(modified_files)[:5]}...")  # Log first 5 files

        # Filter chunks to only include those from modified files
        modified_chunks = []
        for chunk in all_chunks:
            chunk_file = chunk.metadata.file
            # Normalize file path for comparison
            from client_server.utils.file_timestamp_manager import normalize_path_format
            normalized_chunk_file = normalize_path_format(chunk_file)

            if normalized_chunk_file in modified_files:
                modified_chunks.append(chunk)

        LOGGER.info(f"Filtered to {len(modified_chunks)} chunks from modified files")
        return modified_chunks

    except Exception as e:
        LOGGER.error(f"Error filtering modified chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        # On error, return all chunks to be safe
        return all_chunks


async def handle_sync_to_cloud_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):
    """Handle syncing an existing cloud knowledge base with incremental updates."""

    LOGGER.info(f"Starting cloud sync process for KB: {cloud_sync_data.kb_id}")

    # -----------------------------------------------------------------------------------------
    # 1. Validate Knowledge Base for Cloud Sync ----------------------------------------------
    # -----------------------------------------------------------------------------------------

    is_valid, error_message, kb = _validate_kb_for_cloud_sync(cloud_sync_data.kb_id)
    if not is_valid:
        LOGGER.error(f"KB validation failed: {error_message}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=error_message,
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 2. Retrieve All Chunks from Knowledge Base --------------------------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=10,
            message="Retrieving knowledge base chunks",
        ).model_dump(),
        to=sid,
    )

    try:
        chunks_retrieval_start = time.time()
        LOGGER.info(f"Retrieving all chunks for KB: {kb.name}")
        all_chunks = kb.get_all_chunks()
        chunks_retrieval_time = time.time() - chunks_retrieval_start

        LOGGER.info(
            f"Retrieved {len(all_chunks)} total chunks from KB {kb.name} in {chunks_retrieval_time:.2f}s"
        )

        if not all_chunks:
            raise ValueError("No chunks found in knowledge base")

    except Exception as e:
        LOGGER.error(f"Error retrieving chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to retrieve knowledge base chunks: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 3. Filter Chunks Based on File Timestamps (Incremental Sync) -------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=30,
            message="Analyzing modified files for incremental sync",
        ).model_dump(),
        to=sid,
    )

    try:
        filter_start = time.time()
        modified_chunks = _get_modified_chunks_for_sync(kb, all_chunks)
        filter_time = time.time() - filter_start

        LOGGER.info(
            f"Filtered to {len(modified_chunks)} modified chunks out of {len(all_chunks)} total chunks in {filter_time:.2f}s"
        )

        if not modified_chunks:
            # No modified chunks, sync is already up to date
            LOGGER.info("No modified chunks found - knowledge base is already up to date")
            await sio.emit(
                "sync_to_cloud:success",
                data=CloudSyncResponseData(
                    request_id=cloud_sync_data.request_id,
                    status="success",
                    message="Knowledge base is already up to date - no sync needed",
                    data={
                        "total_chunks": len(all_chunks),
                        "modified_chunks": 0,
                        "sync_skipped": True
                    },
                ).model_dump(),
                to=sid,
            )
            return

    except Exception as e:
        LOGGER.error(f"Error filtering modified chunks: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to analyze modified files: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 4. Prepare Update Payload for Cloud API -----------------------------------------------
    # -----------------------------------------------------------------------------------------

    await sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=50,
            message="Preparing incremental update payload",
        ).model_dump(),
        to=sid,
    )

    try:
        # Prepare vectors, metadata, and IDs for modified chunks only
        vectors = []
        metadata = []
        ids = []

        for chunk in modified_chunks:
            vectors.append(chunk.embeddings)
            metadata.append({
                "id": chunk.metadata.id,
                "content": chunk.metadata.content,
                "file": chunk.metadata.file,
                "name": chunk.metadata.name,
                "additional_metadata": chunk.metadata.additional_metadata,
            })
            ids.append(chunk.metadata.id)

        # Create update payload with exact schema
        current_timestamp = int(time.time())
        update_payload = {
            "collection_id": kb.cloud_id,  # Use the KB's cloud_id
            "update_queries": {
                "vectors": vectors,
                "metadata": metadata,
                "ids": ids
            },
            "timestamp": current_timestamp
        }

        LOGGER.info(f"Prepared update payload with {len(modified_chunks)} modified chunks")
        LOGGER.debug(f"Update payload structure: collection_id={kb.cloud_id}, chunks={len(modified_chunks)}, timestamp={current_timestamp}")

    except Exception as e:
        LOGGER.error(f"Error preparing update payload: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to prepare update payload: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 5. Send Update Request to Cloud API ---------------------------------------------------
    # -----------------------------------------------------------------------------------------

    cloud_sync_start = time.time()
    LOGGER.info("Starting incremental sync to cloud")

    progress_fn = lambda progress: sio.emit(
        "sync_to_cloud:progress",
        data=CloudSyncProgressData(
            request_id=cloud_sync_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=progress,
            message="Syncing modified chunks to cloud",
        ).model_dump(),
        to=sid,
    )

    try:
        await progress_fn(70)

        # Get session for authentication
        session = cloud_sync_data.session
        if not session:
            raise ValueError("Session is required for cloud sync authentication")

        LOGGER.debug(f"Syncing to cloud with session: {session}")

        # Import required modules for HTTP request
        import httpx
        from client_server.core.state import G_BASE_URL
        from client_server.core.constants import SSL_CERT_FILE

        base_url = G_BASE_URL.get().cloud
        LOGGER.info(f"Sending incremental sync request to: {base_url}/knowledge/update")

        async with httpx.AsyncClient(
            verify=SSL_CERT_FILE,
            timeout=300.0,  # 5 minute timeout
        ) as client:
            response = await client.post(
                f"{base_url}/knowledge/update",
                json=update_payload,
                headers={
                    "x-session": session,
                    "Content-Type": "application/json",
                },
            )

        await progress_fn(90)

        LOGGER.info(f"Cloud sync response: {response.status_code}")

        if response.status_code != 200:
            response_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {"error": response.text}
            error_message = response_data.get("error", f"HTTP {response.status_code}")
            raise RuntimeError(f"Cloud sync failed: {error_message}")

        response_data = response.json()
        LOGGER.info(f"Cloud sync successful: {response_data}")

        # Update lastSynced timestamp after successful sync
        kb.syncConfig.lastSynced = current_timestamp * 1000  # Convert to milliseconds
        kb.save()

        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.info(
            f"Incremental cloud sync completed successfully in {cloud_sync_time:.2f} seconds"
        )

    except Exception as e:
        cloud_sync_time = time.time() - cloud_sync_start
        LOGGER.error(f"ERROR FROM incremental sync_to_cloud after {cloud_sync_time:.2f}s: {e}")
        LOGGER.error(f"Cloud sync error traceback: {traceback.format_exc()}")
        await sio.emit(
            "sync_to_cloud:error",
            data=CloudSyncResponseData(
                request_id=cloud_sync_data.request_id,
                status="error",
                message=f"Failed to sync to cloud: {e}",
            ).model_dump(),
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 6. Report Success to the Client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    LOGGER.info(
        f"Incremental cloud sync process completed successfully for knowledge base: {kb.name}"
    )
    LOGGER.info(f"Synced {len(modified_chunks)} modified chunks out of {len(all_chunks)} total chunks")

    # Report success to the client
    await sio.emit(
        "sync_to_cloud:success",
        data=CloudSyncResponseData(
            request_id=cloud_sync_data.request_id,
            status="success",
            message="Knowledge base synced to cloud successfully",
            data={
                "total_chunks": len(all_chunks),
                "modified_chunks": len(modified_chunks),
                "sync_time_seconds": cloud_sync_time,
                "last_synced": kb.syncConfig.lastSynced,
                "kb_data": kb.model_dump()
            },
        ).model_dump(),
        to=sid,
    )
    LOGGER.info(
        f"Success event emitted to client for request: {cloud_sync_data.request_id}"
    )