from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import socketio
from BASE.events.kb.upload_kb import handle_upload_event
from BASE.events.kb.cloud_kb_upload import handle_upload_event_to_cloud
from BASE.events.kb.cloud_sync_kb import handle_sync_event_to_cloud

app = FastAPI(title="CodeMate Socket.IO API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# Create Socket.IO server
sio = socketio.AsyncServer(cors_allowed_origins="*")

@sio.event
async def connect(sid, _environ=None):
    """Handle client connection"""
    pass

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    pass

@sio.on("upload")
async def handle_upload(sid, data):
    """Handle upload events using the refactored upload functionality"""
    await handle_upload_event(sio, sid, data)

@sio.on("upload_to_cloud")
async def handle_upload_to_cloud(sid, data):
    """Handle upload events using the refactored upload functionality"""
    await handle_upload_event_to_cloud(sio, sid, data)

@sio.on("sync_to_cloud")
async def handle_sync_to_cloud(sid, data):
    """Handle upload events using the refactored upload functionality"""
    await handle_sync_event_to_cloud(sio, sid, data)

# Mount Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    uvicorn.run(socket_app, host="127.0.0.1", port=45214)

