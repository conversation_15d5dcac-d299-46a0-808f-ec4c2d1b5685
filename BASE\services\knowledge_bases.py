from pathlib import Path
from database.MicroMongo import MongoClient
from BASE.vdb.qdrant import get_qdrant_client
from qdrant_client.models import Distance, VectorParams
from logger.log import logger

# Initialize MicroMongo client and database
_client = None
_db = None

def get_knowledge_bases_collection():
    """Get the knowledge_bases collection from MicroMongo database."""
    global _client, _db

    if _client is None:
        _client = MongoClient('knowledge_bases_db')
        _db = _client['codemate']

    return _db['knowledge_bases']

def create_knowledge_base(name: str, kbid: str, metadata: dict = None, is_auto_indexed: bool = False):
    """Create a new knowledge base entry."""
    collection = get_knowledge_bases_collection()

    kb_data = {
        "name": name,
        "id": kbid,
        "metadata": metadata or {},
        "isAutoIndexed": is_auto_indexed
    }

    result = collection.insert_one(kb_data)
    return result.inserted_id

def get_knowledge_base_by_name(name: str):
    """Get a knowledge base by name."""
    collection = get_knowledge_bases_collection()
    return collection.find_one({"name": name})

def get_knowledge_base_by_id(kbid: str):
    """Get a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.find_one({"id": kbid})

def get_knowledge_base_by_path(path: str):
    """Get a knowledge base by path."""
    collection = get_knowledge_bases_collection()
    normalized_path = str(Path(path).resolve())
    return collection.find_one({"metadata.path": normalized_path})

def get_auto_indexed_knowledge_base_by_path(path: str):
    """Get an auto-indexed knowledge base by path."""
    collection = get_knowledge_bases_collection()
    normalized_path = str(Path(path).resolve())
    return collection.find_one({
        "metadata.path": normalized_path,
        "isAutoIndexed": True
    })

def exists_knowledge_base_by_name(name: str) -> bool:
    """Check if a knowledge base with the given name exists."""
    return get_knowledge_base_by_name(name) is not None

def exists_knowledge_base_by_id(kbid: str) -> bool:
    """Check if a knowledge base with the given ID exists."""
    return get_knowledge_base_by_id(kbid) is not None

def exists_knowledge_base_by_path(path: str) -> bool:
    """Check if a knowledge base with the given path exists."""
    return get_knowledge_base_by_path(path) is not None

def exists_auto_indexed_knowledge_base_by_path(path: str) -> bool:
    """Check if an auto-indexed knowledge base with the given path exists."""
    return get_auto_indexed_knowledge_base_by_path(path) is not None

def update_knowledge_base(kbid: str, update_data: dict):
    """Update a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": update_data})

def update_knowledge_base_cloud_id(kbid: str, cloud_id: str = None):
    """Update the cloud_id field of a knowledge base."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": {"cloud_id": cloud_id}})

def delete_knowledge_base(kbid: str):
    """Delete a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.delete_one({"id": kbid})

def list_all_knowledge_bases():
    """Get all knowledge bases."""
    collection = get_knowledge_bases_collection()
    return list(collection.find())

def count_knowledge_bases():
    """Count total number of knowledge bases."""
    collection = get_knowledge_bases_collection()
    return collection.count_documents({})

@logger.catch()
async def from_chunks_functional(metadata: dict, chunks: list[dict]) -> dict:
    """
    Save processed chunks to Qdrant database using functional programming approach.

    Args:
        metadata: Dictionary containing knowledge base metadata with structure:
            {
                "id": str,
                "cloud_id": None,
                "name": str,
                "description": str,
                "type": str,
                "source": str,
                "scope": str,
                "syncConfig": {"enabled": bool, "lastSynced": int},
                "isAutoIndexed": bool,
                "metadata": {...}  # Type-specific metadata
            }
        chunks: List of chunk dictionaries with structure:
            {
                "metadata": {
                    "id": str,
                    "file": str,
                    "name": str,
                    "content": str,
                    "additional_metadata": {...}
                },
                "embeddings": [float, ...]
            }

    Returns:
        Dictionary representing the created knowledge base
    """
    if not chunks:
        raise ValueError("No chunks provided")

    kb_id = metadata["id"]
    vector_size = len(chunks[0]["embeddings"])

    logger.info(f"Creating Qdrant collection for knowledge base: {kb_id}")

    # Get Qdrant client and create collection
    qdrant_client = get_qdrant_client()
    collection_name = kb_id

    await qdrant_client.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
    )

    logger.info(f"Qdrant collection '{collection_name}' created successfully")

    # Store chunks with embeddings
    points = []
    for i, chunk in enumerate(chunks):
        points.append({
            "id": i,
            "vector": chunk["embeddings"],
            "payload": chunk["metadata"]
        })

    if points:
        logger.info(f"Storing {len(points)} points in Qdrant collection")
        await qdrant_client.upsert(collection_name=collection_name, points=points)
        logger.info("All chunks stored in Qdrant successfully")
    else:
        logger.warning("No points to store in Qdrant")

    # Create knowledge base entry in database
    kb_data = {
        "name": metadata["name"],
        "id": kb_id,
        "metadata": metadata,
        "isAutoIndexed": metadata.get("isAutoIndexed", False),
        "status": "READY"
    }

    collection = get_knowledge_bases_collection()
    result = collection.insert_one(kb_data)
    logger.info(f"Knowledge base entry created in database with ID: {result.inserted_id}")

    return kb_data

def from_chunks(
        metadata: QdrantKnowledgeBaseMetadata,
        chunks: list[QdrantKnowledgeBaseChunk],
    ) -> "QdrantKnowledgeBase":
        # if not processed_data:
        #     raise ValueError("No processed data provided")

        """Save processed chunks to local Qdrant database."""
        vector_size = len(chunks[0].embeddings)
        # print(vector_size)

        client = get_db_client()
        client.create_collection(
            collection_name=metadata.id,
            vectors_config={
                "vectors": rest.VectorParams(
                    size=vector_size, distance=rest.Distance.DOT
                )
            },
        )
        for chunk in chunks:
            client.upsert(
                collection_name=metadata.id,
                points=[
                    PointStruct(
                        id=chunk.metadata.id,
                        vector={"vectors": chunk.embeddings},
                        payload={"metadata": chunk.metadata},
                    ),
                ],
            )

        # Convert metadata to a dictionary
        metadata_dict = metadata.model_dump()

        # Create the QdrantKnowledgeBase instance
        kb = QdrantKnowledgeBase(**metadata_dict)
        kb.status = QdrantKnowledgebaseStatus.READY

        # Insert the knowledge base into the database
        QdrantKnowledgeBase._get_kbdb().insert_one(kb.model_dump())

        return kb